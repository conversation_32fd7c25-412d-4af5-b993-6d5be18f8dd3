{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "N7q2b64Zt1JT/i+kp0zKQtSwAeXEad1q7osWZzH2+Lo=", "__NEXT_PREVIEW_MODE_ID": "90875bd25e1dea71819589e30094c160", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b276d001b3513b10235228f782102a68238b4dc6fba47c270c99aaf1733ce8df", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4e1a25d8fd189dcb3f622ea23145718464f0e0f2f60f63c08f3be6fc34d561a8"}}}, "instrumentation": null, "functions": {}}
{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_acdacdd7._.js", "server/edge/chunks/[root-of-the-server]__586f2561._.js", "server/edge/chunks/edge-wrapper_1b2467bc.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "N7q2b64Zt1JT/i+kp0zKQtSwAeXEad1q7osWZzH2+Lo=", "__NEXT_PREVIEW_MODE_ID": "b89e6957e1478e1f60bebc3dbee1dcb4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2f44cb77381c89256690ff22716d4c5a8ac71f5ba9e8bfe6485fc1b118270643", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4ab1c846ab690e531fad8d3f4f8eb40af2a1d055cbb7c27f3a9c687956ec9fd2"}}}, "instrumentation": null, "functions": {}}
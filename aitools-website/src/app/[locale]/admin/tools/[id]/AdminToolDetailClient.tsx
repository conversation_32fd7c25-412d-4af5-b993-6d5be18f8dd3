'use client';

import React, { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Tool } from '@/lib/api';
import { Locale } from '@/i18n/config';
import ErrorMessage from '@/components/ErrorMessage';
import SuccessMessage from '@/components/SuccessMessage';
import {
  ArrowLeft,
  ExternalLink,
  CheckCircle,
  XCircle,
  Clock,
  Globe,
  DollarSign
} from 'lucide-react';

interface AdminToolDetailClientProps {
  tool: Tool;
  locale: Locale;
}

export default function AdminToolDetailClient({
  tool,
  locale
}: AdminToolDetailClientProps) {
  const router = useRouter();
  const pathname = usePathname();
  const t = useTranslations('admin');
  const tCategories = useTranslations('categories');

  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleApprove = async () => {
    setIsProcessing(true);
    try {
      setError('');

      const response = await fetch(`/api/admin/tools/${tool._id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reviewedBy: 'admin', // In production, this should be the current logged-in admin
          reviewNotes: t('success.tool_approved'),
          launchDate: new Date().toISOString()
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccessMessage(t('success.tool_approved'));
        // Refresh the page to update the tool status
        router.refresh();
      } else {
        setError(data.error || t('errors.approve_failed'));
      }
    } catch (err) {
      setError(t('errors.network_error'));
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!rejectReason.trim()) {
      setError(t('errors.reject_reason_required'));
      return;
    }

    setIsProcessing(true);
    try {
      setError('');

      const response = await fetch(`/api/admin/tools/${tool._id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reviewedBy: 'admin', // In production, this should be the current logged-in admin
          rejectReason: rejectReason
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccessMessage(t('success.tool_rejected'));
        setShowRejectModal(false);
        setRejectReason('');
        // Refresh the page to update the tool status
        router.refresh();
      } else {
        setError(data.error || t('errors.reject_failed'));
      }
    } catch (err) {
      setError(t('errors.network_error'));
    } finally {
      setIsProcessing(false);
    }
  };

  const getStatusBadge = (tool: { status: string; launchDate?: string }) => {
    // Check if published: approved status and launchDate has passed
    const isPublished = tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= new Date();

    if (isPublished) {
      return (
        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
          <CheckCircle className="w-4 h-4 mr-2" />
          {t('status_labels.published')}
        </span>
      );
    }

    switch (tool.status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
            <Clock className="w-4 h-4 mr-2" />
            {t('status_labels.pending')}
          </span>
        );
      case 'approved':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
            <CheckCircle className="w-4 h-4 mr-2" />
            {t('status_labels.approved')}
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
            <XCircle className="w-4 h-4 mr-2" />
            {t('status_labels.rejected')}
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <>
      {/* Success/Error Messages */}
      {successMessage && (
        <SuccessMessage
          message={successMessage}
          onClose={() => setSuccessMessage('')}
          className="mb-6"
        />
      )}

      {error && (
        <ErrorMessage
          message={error}
          onClose={() => setError('')}
          className="mb-6"
        />
      )}

      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => router.back()}
          className="flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          {t('actions.back_to_review')}
        </button>
        
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-6">
            <img
              src={tool.logo}
              alt={tool.name}
              className="w-24 h-24 rounded-xl object-cover border border-gray-200 shadow-sm"
            />
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-3xl font-bold text-gray-900">{tool.name}</h1>
                {getStatusBadge(tool)}
              </div>
              <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {tCategories(`category_names.${tool.category}`) || tool.category}
                </span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  <DollarSign className="w-3 h-3 mr-1" />
                  {t(`pricing_labels.${tool.pricing}`) || tool.pricing}
                </span>
                <a
                  href={tool.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
                >
                  <Globe className="w-4 h-4 mr-1" />
                  {t('actions.visit_website')}
                  <ExternalLink className="w-3 h-3 ml-1" />
                </a>
              </div>
              <p className="text-gray-600 max-w-3xl">{tool.tagline}</p>
            </div>
          </div>

          {/* Action Buttons */}
          {tool.status === 'pending' && (
            <div className="flex space-x-3">
              <button
                onClick={handleApprove}
                disabled={isProcessing}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                {isProcessing ? t('actions.processing') : t('actions.approve')}
              </button>
              <button
                onClick={() => setShowRejectModal(true)}
                disabled={isProcessing}
                className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
              >
                <XCircle className="w-4 h-4 mr-2" />
                {t('actions.reject')}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Reject Modal */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('reject_modal.title')}</h3>
            <p className="text-sm text-gray-600 mb-4">
              {t('reject_modal.description')}
            </p>
            <textarea
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder={t('reject_modal.placeholder')}
            />
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowRejectModal(false);
                  setRejectReason('');
                }}
                disabled={isProcessing}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                {t('actions.cancel')}
              </button>
              <button
                onClick={handleReject}
                disabled={!rejectReason.trim() || isProcessing}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {isProcessing ? t('actions.processing') : t('actions.confirm_reject')}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
